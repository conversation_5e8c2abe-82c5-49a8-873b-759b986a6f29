{"name": "highground-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "next build", "start": "next start --port 3005", "lint": "next lint", "check-types": "tsc --noEmit"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@mendable/firecrawl-js": "^1.29.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "echarts": "^5.6.0", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "next": "15.3.3", "next-auth": "^4.24.11", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.8", "@types/lodash": "^4.17.17", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8", "prettier": "^3.5.3", "sass": "^1.89.1", "typescript": "^5"}}