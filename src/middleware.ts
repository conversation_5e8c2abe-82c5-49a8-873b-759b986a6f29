import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { clearStaleSession, isStaleToken, debugNextAuthCookies } from '@/lib/utils/session-cleanup';

const secret = process.env.NEXTAUTH_SECRET;

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for auth routes to prevent infinite loops
  const isAuthRoute = pathname.startsWith('/login') || pathname.startsWith('/api/auth');
  if (isAuthRoute) {
    return NextResponse.next();
  }


  if (!secret) {
    console.error('NEXTAUTH_SECRET is not set');
    return NextResponse.redirect(new URL('/login', request.url));
  }

  try {
    // Get the current session token
    const token = await getToken({
      req: request,
      secret,
    });

    // If no token, redirect to login
    if (!token) {
      return NextResponse.redirect(new URL('/login', request.url));
    }

    // Check if token is stale/corrupted - clear aggressively
    if (isStaleToken(token)) {
      console.log('Stale token detected, clearing session');
      debugNextAuthCookies(request);
      return clearStaleSession(request);
    }

    // Check if we should sign out (both tokens expired or invalid)
    if (shouldSignOut(token)) {
      console.log('Token expired, signing out');
      debugNextAuthCookies(request);
      return clearStaleSession(request);
    }

    let response = NextResponse.next();

    // Set the current path header
    const fullPath = `${request.nextUrl.pathname}${request.nextUrl.search}`;
    response.headers.set('x-current-path', fullPath);

    // Check if token needs refresh
    if (shouldRefreshToken(token)) {
      console.log('Token needs refresh - letting NextAuth handle it on next request');
      // The JWT callback in nextauth.config.ts will handle the refresh
      // This prevents JWT encoding issues and cookie splitting problems
    }

    // Check for very old tokens that should be cleared
    const accessTokenExpires = token.accessTokenExpires as number;
    if (accessTokenExpires) {
      const tokenAge = Date.now() - accessTokenExpires;
      const maxStaleAge = 24 * 60 * 60 * 1000; // 24 hours
      if (tokenAge > maxStaleAge) {
        console.log(`Token is extremely old (${Math.round(tokenAge / 1000)}s), clearing session`);
        debugNextAuthCookies(request);
        return clearStaleSession(request);
      }
    }

    return response;
  } catch (error) {
    console.error('Middleware error:', error);
    return clearStaleSession(request);
  }
}

function shouldSignOut(token: any): boolean {
  // If no access token and no refresh token, sign out
  if (!token.accessToken && !token.refreshToken) {
    return true;
  }

  // If there's a refresh token error, sign out immediately
  if (token.error === 'RefreshAccessTokenError') {
    return true;
  }

  // If both tokens are expired, sign out
  const now = Date.now();
  const accessTokenExpired = !token.accessToken || now >= (token.accessTokenExpires || 0);
  const refreshTokenExpired = !token.refreshToken || (token.refreshTokenExpires && now >= token.refreshTokenExpires);

  return accessTokenExpired && refreshTokenExpired;
}

function shouldRefreshToken(token: any): boolean {
  // Don't try to refresh if there's already an error
  if (token.error === 'RefreshAccessTokenError') {
    return false;
  }

  // Don't try to refresh if no refresh token
  if (!token.refreshToken) {
    return false;
  }

  // Check if access token is about to expire (with 1-minute buffer)
  const now = Date.now();
  const accessTokenExpires = token.accessTokenExpires || 0;
  const bufferTime = 60 * 1000; // 1 minute buffer

  // Refresh if token is expired or about to expire within buffer time
  const isExpired = now >= (accessTokenExpires - bufferTime);

  if (!isExpired) {
    return false;
  }

  // Check if refresh token itself is expired
  const refreshTokenExpires = token.refreshTokenExpires;
  if (refreshTokenExpires && now >= refreshTokenExpires) {
    console.log('Refresh token has expired, not attempting refresh');
    return false;
  }

  // For very short-lived tokens (like 10 seconds), be more lenient with age checks
  const tokenLifespan = (token.expiresIn || 3600) * 1000; // Convert to milliseconds
  const timeSinceExpiry = now - accessTokenExpires;

  // Allow refresh if it's been less than 10 minutes since expiry OR if token lifespan is very short
  const maxStaleTime = tokenLifespan < 60000 ? 10 * 60 * 1000 : 60 * 60 * 1000; // 10 min for short tokens, 1 hour for normal

  if (timeSinceExpiry > maxStaleTime) {
    console.log(`Access token expired ${Math.round(timeSinceExpiry / 1000)}s ago, too old to refresh`);
    return false;
  }

  return true;
}


export const config = {
  matcher: ['/main/:path*'],
};
