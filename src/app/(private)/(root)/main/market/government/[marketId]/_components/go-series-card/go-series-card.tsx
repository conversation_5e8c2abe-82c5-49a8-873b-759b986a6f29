'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/card';
import { cn } from '@/lib/styles/utils';
import { GOSeriesBaseballCard } from './components/go-series-baseball-card';
import { ScrollArea } from '@/components/scroll-area';
import { Button, Skeleton } from '@/components';
import { Undo2Icon } from 'lucide-react';
import { Pie<PERSON>hart } from '@/components/charts/pie-chart';
import { v4 as uuidv4 } from 'uuid';
import { newSeriesName } from '@/app/(private)/(root)/main/market/venture-capital/[marketId]/_components/vc-series-card/vc-series-card';
import React, { ReactNode, useState, useEffect } from 'react';
import { useSeriesContracts } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-series-card/components/use-series-contracts';
import { Contract } from '@/app/(private)/(root)/main/market/government/[marketId]/_actions/filter-contracts';
import {
  SeriesContractsResponse
} from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-series-card/types/series-contracts-response.type';
import { useSeriesContractsFiltered } from '@/app/(private)/(root)/main/market/government/[marketId]/_hooks/use-series-contracts-filtered';
import { useFilteredContracts } from '@/app/(private)/(root)/main/market/government/[marketId]/_hooks/use-filtered-contracts';

type GOMarketSeriesCardProps = {
  seriesName: string;
  className?: string;
  contractTypes: Record<string, number>;
  children?: ReactNode;
  marketId: number;
  selectedOffices?: string[];
  selectedServices?: string[];
  seriesLetter?: string; // A, B, C, or D
};

export function GOSeriesCard({
  seriesName,
  className,
  contractTypes,
  children,
  marketId,
  selectedOffices,
  selectedServices,
  seriesLetter,
}: GOMarketSeriesCardProps) {
  // Use filtered data when offices are selected, otherwise use static data
  const { data: filteredData, isLoading: isLoadingFiltered } = useSeriesContractsFiltered(
    marketId,
    selectedOffices,
    seriesLetter ? [seriesLetter] : undefined
  );

  // Determine which data to use - filtered data takes precedence when offices are selected
  const shouldUseFilteredData = selectedOffices && selectedOffices.length > 0 && seriesLetter;
  const currentContractTypes = shouldUseFilteredData && filteredData && seriesLetter
    ? filteredData.fundingDetails[getSeriesStage(seriesLetter)] || {}
    : contractTypes;

  const mappedArray = Object.entries(currentContractTypes).map(([key, value]) => ({
    value: value,
    name: key,
  }));

  const [tableMode, setTableMode] = useState(false);
  const [contractsToShow, setContractsToShow] = useState<Contract[]>([]);
  const [isLoadingContracts, setIsLoadingContracts] = useState(false);
  const { getContractsByType, isMutating, data } = useSeriesContracts();
  const { getFilteredContracts, isMutating: isFilteredMutating } = useFilteredContracts();

  // Reset to chart mode when filtering parameters change
  useEffect(() => {
    if (tableMode) {
      setTableMode(false);
      setContractsToShow([]);
      setIsLoadingContracts(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedOffices, selectedServices]); // Reset when offices or services change

  // Helper function to map series letter to stage name
  function getSeriesStage(letter: string): string {
    switch (letter) {
      case 'A': return 'Early Stage';
      case 'B': return 'Mid Stage';
      case 'C': return 'Growth';
      case 'D': return 'Enterprise';
      default: return 'Early Stage';
    }
  }

  const handleOnSliceClick = async (item: Record<string, Record<string, unknown>>) => {
    setTableMode(true);

    const contractType = item['data']['name'] as string;

    try {
      // Use the new contracts list endpoint when we have marketId and seriesLetter
      if (marketId && seriesLetter) {
        setIsLoadingContracts(true);
        const result = await getFilteredContracts({
          marketId: marketId + '',
          offices: selectedOffices && selectedOffices.length > 0 ? selectedOffices : undefined,
          series: [seriesLetter],
          contractType,
        });
        // Direct result from authGet - type assertion needed
        const typedResult = result as { contracts: Contract[] };
        setContractsToShow(typedResult.contracts || []);
      } else {
        // Fallback to old endpoint for backward compatibility (e.g., venture capital page)
        await getContractsByType({ types: contractType });
        // This will update the 'data' from the hook
      }
    } catch (error) {
      console.error('Error fetching contracts:', error);
      setContractsToShow([]);
    } finally {
      setIsLoadingContracts(false);
    }
  };

  return (
    <div className={cn('series-wrapper', className)}>
      <Card className="flex-1 shrink-0 mb-4 rounded-md shadow-none">
        <CardHeader className="p-4 border-b">
          <CardTitle className="flex items-center justify-between">
            <span
              className="font-medium"
              style={{ fontFamily: 'var(--onest)' }}
            >
              {newSeriesName(seriesName)}
            </span>

            <span className="h-[32px]">
              {tableMode && (
                <Button
                  onClick={() => {
                    setTableMode(false);
                    setContractsToShow([]); // Clear contracts when going back to chart
                    setIsLoadingContracts(false); // Clear loading state
                  }}
                  variant="ghost"
                  size="sm"
                >
                  <Undo2Icon />
                </Button>
              )}
            </span>
          </CardTitle>
        </CardHeader>

        <CardContent className={cn('px-4 pb-0', !!children && 'pb-4')}>
          {isLoadingFiltered && shouldUseFilteredData ? (
            <div className="flex justify-center items-center h-[254px]">
              <Skeleton className="w-full h-[200px]" />
            </div>
          ) : tableMode ? (
            <ul>
              <li className="border-b border-t p-2 pb-3 bg-slate-200 font-bold">
                Contracts
              </li>

              <ScrollArea className="h-[224px] overflow-auto">
                {!isMutating && !isFilteredMutating && !isLoadingContracts && (
                  <React.Fragment>
                    {/* Show contracts from state (filtered) or from SWR data (old endpoint) */}
                    {contractsToShow.length > 0 ? (
                      contractsToShow.map((award: Contract) => (
                        <li
                          key={uuidv4()}
                          className="border-b p-3 pb-3"
                        >
                          <GOSeriesBaseballCard award={award}>
                            <span>{award.contractId}</span>
                          </GOSeriesBaseballCard>
                        </li>
                      ))
                    ) : data ? (
                      ((data as SeriesContractsResponse).contracts as Contract[]).map(
                        (award: Contract) => (
                          <li
                            key={uuidv4()}
                            className="border-b p-3 pb-3"
                          >
                            <GOSeriesBaseballCard award={award}>
                              <span>{award.contractId}</span>
                            </GOSeriesBaseballCard>
                          </li>
                        ),
                      )
                    ) : null}
                  </React.Fragment>
                )}

                {(isMutating || isFilteredMutating || isLoadingContracts) && (
                  <React.Fragment>
                    <Skeleton className="w-full h-[45px] mb-2 mt-2" />

                    <Skeleton className="w-full h-[45px] mb-2" />

                    <Skeleton className="w-full h-[45px] mb-2" />

                    <Skeleton className="w-full h-[45px] mb-2" />
                  </React.Fragment>
                )}
              </ScrollArea>
            </ul>
          ) : (
            <PieChart
              height="270px"
              data={mappedArray}
              onClick={handleOnSliceClick}
            />
          )}
          {!!children && <div className="other">{children}</div>}
        </CardContent>
      </Card>
    </div>
  );
}
