'use client';

import { MarketTitleSection } from '../../../../_components/market-title-section';
import { GOServiceList } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-service-list/go-service-list';
import { GOChartSection } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-chart-section/go-chart-section';
import { GOProgramList } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-program-list/go-program-list';
import { GOSeriesCard } from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-series-card/go-series-card';
import { Suspense, useMemo } from 'react';
import GOChartSectionLoading from '@/app/(private)/(root)/main/market/government/[marketId]/_components/go-chart-section/loading';
import { MarketValue } from '@/app/(private)/(root)/main/market/_lib/types/market.enum';
import { useSearchParams } from 'next/navigation';

type GovernmentDashboardClientProps = {
  marketId: string;
  initialData: {
    marketTitle: string;
    marketDescription: string | null;
    fundingDetails: Record<string, Record<string, number>>;
  };
  path: string;
};

export function GovernmentDashboardClient({
  marketId,
  initialData,
  path,
}: GovernmentDashboardClientProps) {
  const searchParams = useSearchParams();

  // Get current search params
  const servicesParam = useMemo(() => {
    const services = searchParams.get('services');
    return services ? services.split(',').filter(Boolean) : [];
  }, [searchParams]);

  const programsParam = searchParams.get('programs');
  const servicesParamString = searchParams.get('services');

  // Get selected offices directly from search params
  const selectedOffices = useMemo(() => {
    const programs = searchParams.get('programs');
    return programs ? programs.split(',').filter(Boolean) : [];
  }, [searchParams]);

  return (
    <div className="market-page pt-4 py-12 px-8 bg-[#F9F9F9]">
      <div className="wrapper">
        <div className="dashboard-header">
          <MarketTitleSection
            title={initialData.marketTitle}
            marketType={MarketValue.GOVERNMENT}
            marketId={marketId}
            pathToFav={path}
          />
        </div>

        <div className="market-details mb-6 flex">
          <div className="top-pane w-full flex gap-x-6">
            <div className="left-side shrink-0 grow-0 basis-[360px] max-w-[360px] [&>div+div]:mt-4">
              <GOServiceList marketId={marketId} />

              <GOProgramList
                marketId={marketId}
                selectedServices={servicesParam}
              />
            </div>

            <section className="chart-section h-full flex-1 shrink-1 basis-auto">
              <Suspense fallback={<GOChartSectionLoading />}>
                <GOChartSection
                  height="515px"
                  programsParams={programsParam || ''}
                  marketId={marketId}
                  servicesParams={servicesParamString || ''}
                />
              </Suspense>
            </section>
          </div>
        </div>

        <div className="bottom-pane series-market flex gap-x-6 overflow-x-auto">
          <GOSeriesCard
            key="series-a"
            contractTypes={initialData.fundingDetails['Early Stage'] ?? {}}
            className="flex-0 shrink-0 max-2xl:basis-[426px] max-2xl:max-w-[426px] 2xl:flex-1 2xl:shrink-1"
            seriesName="Series A"
            marketId={+marketId}
            selectedOffices={selectedOffices}
            selectedServices={servicesParam}
            seriesLetter="A"
          />

          <GOSeriesCard
            key="series-b"
            contractTypes={initialData.fundingDetails['Mid Stage'] ?? {}}
            className="flex-0 shrink-0 max-2xl:basis-[426px] max-2xl:max-w-[426px] 2xl:flex-1 2xl:shrink-1"
            seriesName="Series B"
            marketId={+marketId}
            selectedOffices={selectedOffices}
            selectedServices={servicesParam}
            seriesLetter="B"
          />

          <GOSeriesCard
            key="series-c"
            contractTypes={initialData.fundingDetails['Growth'] ?? {}}
            className="flex-0 shrink-0 max-2xl:basis-[426px] max-2xl:max-w-[426px] 2xl:flex-1 2xl:shrink-1"
            seriesName="Series C"
            marketId={+marketId}
            selectedOffices={selectedOffices}
            selectedServices={servicesParam}
            seriesLetter="C"
          />

          <GOSeriesCard
            key="series-d"
            contractTypes={initialData.fundingDetails['Enterprise'] ?? {}}
            className="flex-0 shrink-0 max-2xl:basis-[426px] max-2xl:max-w-[426px] 2xl:flex-1 2xl:shrink-1"
            seriesName="Series D"
            marketId={+marketId}
            selectedOffices={selectedOffices}
            selectedServices={servicesParam}
            seriesLetter="D"
          />
        </div>
      </div>
    </div>
  );
}
