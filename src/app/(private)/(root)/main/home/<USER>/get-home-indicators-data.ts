'use server';

import { serverAuthGet } from '@/lib/utils/server-auth-fetch';

type HomeIndicators = {
  markets: number;
  customers: number;
  companies: number;
  contracts: number;
  opportunities: number;
};

export async function getHomeIndicatorsData(): Promise<HomeIndicators> {
  try {
    return await serverAuthGet<HomeIndicators>('/market/indicators');
  } catch (e) {
    console.error('Error fetching home indicators, using fallback data:', e);

    // Return fallback data directly
    return {
      markets: 809,
      companies: 1203,
      contracts: 5093,
      customers: 5293,
      opportunities: 3283,
    };
  }
}
