import { UserApprovalTable } from '@/app/(private)/(root)/main/admin/user-approval/_components/user-table/user-approval-table';
import { columns } from '@/app/(private)/(root)/main/admin/user-approval/_components/user-table/columns';
import { getApprovalList } from '@/app/(private)/(root)/main/admin/user-approval/_actions/get-approval-list';
import { checkAdminAccess } from '@/lib/utils/admin-guard';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Approval List | HighGround',
};

export default async function UserApprovalPage() {
  await checkAdminAccess();

  const data = await getApprovalList();

  return (
    <div className="user-approval-page">
      <UserApprovalTable
        data={data}
        columns={columns}
      />
    </div>
  );
}
