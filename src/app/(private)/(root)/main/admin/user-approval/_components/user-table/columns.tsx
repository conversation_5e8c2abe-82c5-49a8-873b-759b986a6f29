'use client';

import { ColumnDef, Row } from '@tanstack/react-table';
import { UserApproval } from '@/app/(private)/(root)/main/admin/user-approval/_lib/types/user-approval';
import {
  ArrowUpDown,
  BanIcon,
  CheckIcon,
  ShieldCheckIcon,
  ShieldXIcon,
} from 'lucide-react';
import { Button } from '@/components';
import {
  useApproveUser,
  useDeactivateUser,
} from '@/app/(private)/(root)/main/admin/user-approval/_components/user-table/lib/hooks/use-approval-list';
import { useToast } from '@/components/toast';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/styles/utils';

export const columns: ColumnDef<UserApproval>[] = [
  {
    accessorKey: 'email',
    header: ({ column }) => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Email
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'username',
    header: ({ column }) => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Username
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
  },
  {
    accessorKey: 'enabled',
    id: 'enabled',
    header: ({ column }) => {
      return (
        <Button
          className="px-0"
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Enabled
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const value = row.getValue<string>('enabled');

      return (
        <span>
          {value ? (
            <ShieldCheckIcon className="text-green-500" />
          ) : (
            <ShieldXIcon className="text-red-500" />
          )}
        </span>
      );
    },
  },
  {
    accessorKey: 'actions',
    header: 'Actions',
    cell: ({ row }) => <ActionsCell row={row} />,
  },
];

function ActionsCell({ row }: { row: Row<UserApproval> }) {
  {
    const data = row.original;
    const router = useRouter();
    const { toast } = useToast();
    const { activateUser, isMutating: isApprovingUser } = useApproveUser(
      data.id,
    );
    const { deactivateUser, isMutating: isDeactivatingUser } =
      useDeactivateUser(data.id);

    return (
      <div className="[&>button+button]:ml-2">
        <Button
          title="Enable User"
          size="sm"
          variant="secondary"
          className={cn('shadow-lg', isApprovingUser && 'cursor-progress')}
          type="button"
          onClick={async () => {
            const res = (await activateUser()) as Response;

            if (res.status === 200 || res.status === 201) {
              toast({ title: 'User has been enabled', variant: 'success' });
              router.refresh();
            }
          }}
          disabled={!!row.getValue('enabled') || isApprovingUser}
        >
          <CheckIcon
            size={14}
            className="text-green-500"
          />
        </Button>

        <Button
          title="Disable User"
          size="sm"
          disabled={!row.getValue('enabled') || isDeactivatingUser}
          variant="destructive"
          className={cn('shadow-lg', isDeactivatingUser && 'cursor-progress')}
          type="button"
          onClick={async () => {
            const res = (await deactivateUser<Response>()) as Response;

            if (res.status === 200 || res.status === 201) {
              toast({ title: 'User has been disabled', variant: 'success' });
              router.refresh();
            }
          }}
        >
          <BanIcon
            size={14}
            className="text-white"
          />
        </Button>
      </div>
    );
  }
}
