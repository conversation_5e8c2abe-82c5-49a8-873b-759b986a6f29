import { useSWRMutateWithAuth } from '@/lib/hooks/use-swr-mutate-auth';
import { config } from '@/lib/config';

export function useApproveUser(userId: string) {
  const clientUrl = config.api.base_url;

  const methods = useSWRMutateWithAuth(
    `${clientUrl}/user/activate/${userId}`,
    'POST',
    false,
  );

  return { ...methods, activateUser: methods.trigger };
}

export function useDeactivateUser(userId: string) {
  const clientUrl = config.api.base_url;

  const methods = useSWRMutateWithAuth(
    `${clientUrl}/user/deactivate/${userId}`,
    'POST',
    false,
  );

  return { ...methods, deactivateUser: methods.trigger };
}
