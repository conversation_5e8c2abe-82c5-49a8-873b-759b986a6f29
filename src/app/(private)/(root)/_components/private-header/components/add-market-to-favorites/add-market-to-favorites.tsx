import {
  But<PERSON>,
  Form,
  FormControl,
  FormItem,
  FormLabel,
  Input,
} from '@/components';
import { CheckIcon, Loader2Icon, StarIcon } from 'lucide-react';
import { cn } from '@/lib/styles/utils';
import { MarketValue } from '@/app/(private)/(root)/main/market/_lib/types/market.enum';
import { useForm } from 'react-hook-form';
import { useEffect, useState } from 'react';
import { useToast } from '@/components/toast';
import { useAddToFavorites } from './hooks/use-add-to-favorites';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/dialog';
import { useAppState } from '@/lib/providers/app-state/app-state.provider';
import { useRefreshAppFavorites } from '@/lib/providers/app-state/effects/favorite.effect';
import { Favorite } from '@/lib/types/favorite.type';

type AddMarketToFavoritesProps = {
  className?: string;
  pathToFav: string;
  marketId: string;
  marketType?: MarketValue;
};

export function AddMarketToFavorites({
  className,
  pathToFav,
  marketId,
  marketType,
}: AddMarketToFavoritesProps) {
  const form = useForm<{ favoriteName?: string }>();
  const { addToFavorites, isMutating } = useAddToFavorites(marketId);
  const [modalOpen, setModalOpen] = useState(false);
  const { state } = useAppState();
  const { getUserFavorites } = useRefreshAppFavorites();
  const { toast } = useToast();
  const [favoriteFound, setFavoriteFound] = useState<Favorite | undefined>(
    undefined,
  );

  useEffect(() => {
    const currentFavorites = state.favorites?.[marketType ?? ''] ?? [];

    const favoriteFound = currentFavorites.find(
      (fav) => fav.sourceUrl === pathToFav,
    );

    setFavoriteFound(favoriteFound);
  }, [state.favorites, pathToFav, marketType]);

  const onSubmit = async (data: { favoriteName?: string }) => {
    const response = (await addToFavorites({
      name: data.favoriteName,
      marketId,
      sourceUrl: pathToFav,
      type: marketType,
    })) as Response;

    if (!response.ok) {
      return;
    }

    toast({ title: 'Favorites updated', variant: 'success' });
    setModalOpen(false);
    await getUserFavorites();
  };

  return (
    <Dialog
      open={modalOpen && !favoriteFound}
      onOpenChange={(open) => {
        // Set modalOpen only while favoriteFound is undefined
        setModalOpen(open && !favoriteFound);

        form.reset();
      }}
    >
      {!!state.favorites && (
        <DialogTrigger asChild>
          <Button
            title={!!favoriteFound ? 'Remove from favorites' : 'Add favorites'}
            className={cn(
              className,
              'flex items-center rounded-3xl text-sm border-[#64748B] text-[#64748B]',
              favoriteFound && 'text-[#017EFA] border-[#017EFA] bg-[#E2F1FF]',
            )}
            onClick={async () => {
              if (!!favoriteFound) {
                await addToFavorites(favoriteFound);

                await getUserFavorites();

                toast({ title: 'Removed from favorites', variant: 'success' });
                return;
              }

              setModalOpen(!!favoriteFound);
            }}
            variant="hg_outline"
          >
            {!!favoriteFound ? (
              <>
                <CheckIcon size={18} /> Added to Favorites
              </>
            ) : (
              <>
                <StarIcon size={14} /> Add to Favorites
              </>
            )}
          </Button>
        </DialogTrigger>
      )}

      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add to favorites TBD?</DialogTitle>

          <DialogDescription className="sr-only">
            Add to favorites TBD
          </DialogDescription>
        </DialogHeader>

        <div className="content">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="form-control basis-1/2 max-w-[50%] grow-0 shrink mb-4">
                <FormItem>
                  <FormLabel>Favorite Name</FormLabel>

                  <FormControl>
                    <Input
                      disabled={isMutating}
                      name="favoriteName"
                      id="favoriteName"
                    />
                  </FormControl>
                </FormItem>
              </div>

              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={!form.formState.isValid || isMutating}
                >
                  Save
                  {isMutating && <Loader2Icon className="animate-spin" />}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
