import { Metadata } from 'next';
import { NewAccountForm } from '@/app/(public)/signup/_components/new-account-form/new-account-form';
import { Card, CardContent, CardHeader } from '@/components/card';

export const metadata: Metadata = {
  title: 'Sign Up - HighGround',
};

export default async function SignUpPage() {
  return (
    <div className="signup-page bg-[#E4E8EF] min-h-screen flex justify-center items-center">
      <div className="page-content min-w-[768px] max-w-3xl">
        <div className="page-wrapper py-16">
          <Card>
            <CardHeader>
              <h1 className="text-3xl text-center tracking-tight text-[#3769DA] font-bold">
                Create New Account
              </h1>
            </CardHeader>

            <CardContent>
              <NewAccountForm />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
