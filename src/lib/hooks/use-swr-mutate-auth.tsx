'use client';

import useSWRMutation from 'swr/mutation';
import {
  authDelete,
  authGet,
  authPatch,
  authPost,
  authPut,
} from '@/lib/utils/auth-fetch';

type Method = 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'GET';

/**
 * SWR mutation hook for authenticated API calls
 * @param urlToUse - The URL to make the request to
 * @param method - HTTP method (defaults to POST)
 * @param asJsonResponse - Whether to expect JSON response (applies to POST and PATCH)
 * @returns SWR mutation object with data, isMutating, error, and trigger
 */
export function useSWRMutateWithAuth<TData = unknown, TArg = unknown>(
  urlToUse: string,
  method: Method = 'POST',
  asJsonResponse = true
) {
  const { data, isMutating, error, trigger } = useSWRMutation<
    TData,
    Error,
    string,
    TArg
  >(urlToUse, (url, { arg }: { arg: TArg }) => {
    switch (method) {
      case 'POST':
        return authPost<TData>(url, arg, asJsonResponse);

      case 'PUT':
        return authPut<TData>(url, arg);

      case 'GET':
        return authGet<TData>(url, arg as Record<string, string | number>);

      case 'PATCH':
        return authPatch<TData>(url, arg, asJsonResponse);

      case 'DELETE':
        return authDelete<TData>(url, arg);

      default:
        return authPost<TData>(url, arg, asJsonResponse);
    }
  });

  return { data, isMutating, error, trigger };
}
