'use client';

import useSWRImmutable from 'swr/immutable';
import { authFetchJson } from '@/lib/utils/auth-fetch';

/**
 * S<PERSON> hook for authenticated data fetching with immutable caching
 * @param url - The URL to fetch data from (null to disable the request)
 * @returns SWR object with data, isLoading, and error
 */
export function useSwrAuth<T>(url: string | null) {
  const { data, isLoading, error } = useSWRImmutable<T, Error>(
    url, // SWR automatically handles null URLs by not making requests
    url ? (url) => authFetchJson<T>(url) : null,
    {
      // Add retry configuration for failed requests
      errorRetryCount: 2,
      errorRetryInterval: 1000, // 1 second between retries
      // Revalidate on focus for better UX
      revalidateOnFocus: true,
      // Don't revalidate on reconnect since we use immutable
      revalidateOnReconnect: false,
    },
  );

  return { data, isLoading, error };
}
