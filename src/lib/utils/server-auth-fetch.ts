import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/config/nextauth.config';
import { redirect } from 'next/navigation';
import { config } from '@/lib/config';
import { Session } from 'next-auth';

/**
 * Server-side authenticated fetch wrapper for server actions
 * Handles session validation, authentication headers, and error handling
 */
export async function serverAuthFetch(
  url: string,
  options: RequestInit = {},
): Promise<Response> {
  'use server';
  const session = await getServerSession(authOptions);

  if (!session?.accessToken) {
    console.error('No session or access token available');
    redirect('/login');
  }

  // Check for session errors (like RefreshAccessTokenError)
  if (session.error) {
    console.error('Session error detected:', session.error);
    redirect('/login');
  }

  // Add authorization header
  const headers = {
    ...options.headers,
    Authorization: `Bearer ${session.accessToken}`,
  };

  // Make the request
  const response = await fetch(url, {
    ...options,
    headers,
  });

  // Handle 401 errors by redirecting to login
  if (response.status === 401) {
    console.error('Received 401 Unauthorized, redirecting to login');
    redirect('/login');
  }

  return response;
}

/**
 * Server-side convenience wrapper for JSON API calls
 */
export async function serverAuthFetchJson<T>(
  url: string,
  options: RequestInit = {},
): Promise<T> {
  'use server';
  const response = await serverAuthFetch(url, options);

  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

    // Try to get more detailed error message from response body
    try {
      const errorBody = await response.text();
      if (errorBody) {
        errorMessage += ` - ${errorBody}`;
      }
    } catch {
      // Ignore errors when reading response body
    }

    throw new Error(errorMessage);
  }

  try {
    return await response.json();
  } catch (error) {
    throw new Error(`Failed to parse JSON response: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Server-side GET request with query parameters
 */
export async function serverAuthGet<T>(
  endpoint: string,
  params?: Record<string, string | number | boolean | undefined | null>,
): Promise<T> {
  'use server';
  const baseUrl = config.api.base_url_for_server_side;
  
  if (!baseUrl) {
    throw new Error('Server-side API base URL not configured');
  }

  const finalUrl = params && Object.entries(params).length > 0
    ? `${baseUrl}${endpoint}?${toQueryString(params)}`
    : `${baseUrl}${endpoint}`;

  return serverAuthFetchJson<T>(finalUrl, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * Server-side POST request with JSON body
 */
export async function serverAuthPost<T>(
  endpoint: string,
  data: unknown,
): Promise<T> {
  'use server';
  const baseUrl = config.api.base_url_for_server_side;
  
  if (!baseUrl) {
    throw new Error('Server-side API base URL not configured');
  }

  return serverAuthFetchJson<T>(`${baseUrl}${endpoint}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
}

/**
 * Server-side PUT request with JSON body
 */
export async function serverAuthPut<T>(
  endpoint: string,
  data: unknown,
): Promise<T> {
  'use server';
  const baseUrl = config.api.base_url_for_server_side;
  
  if (!baseUrl) {
    throw new Error('Server-side API base URL not configured');
  }

  return serverAuthFetchJson<T>(`${baseUrl}${endpoint}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
}

/**
 * Server-side PATCH request with JSON body
 */
export async function serverAuthPatch<T>(
  endpoint: string,
  data: unknown,
): Promise<T> {
  'use server';
  const baseUrl = config.api.base_url_for_server_side;
  
  if (!baseUrl) {
    throw new Error('Server-side API base URL not configured');
  }

  return serverAuthFetchJson<T>(`${baseUrl}${endpoint}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
}

/**
 * Server-side DELETE request
 */
export async function serverAuthDelete<T>(
  endpoint: string,
  data?: unknown,
): Promise<T> {
  'use server';
  const baseUrl = config.api.base_url_for_server_side;
  
  if (!baseUrl) {
    throw new Error('Server-side API base URL not configured');
  }

  const requestOptions: RequestInit = {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (data) {
    requestOptions.body = JSON.stringify(data);
  }

  return serverAuthFetchJson<T>(`${baseUrl}${endpoint}`, requestOptions);
}

/**
 * Helper function to convert parameters to query string
 */
export function toQueryString(
  params: Record<string, string | number | boolean | undefined | null>,
): string {
  return Object.entries(params)
    .filter(([, value]) => value !== undefined && value !== null)
    .map(
      ([key, value]) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`,
    )
    .join('&');
}

/**
 * Helper function to get current session with type safety
 * Throws error if no session is available
 */
export async function requireServerSession(): Promise<Session> {
  const session = await getServerSession(authOptions);

  if (!session?.accessToken) {
    console.error('No session or access token available');
    redirect('/login');
  }

  if (session.error) {
    console.error('Session error detected:', session.error);
    redirect('/login');
  }

  return session;
}
